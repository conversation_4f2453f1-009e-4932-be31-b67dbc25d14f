import 'package:flutter/material.dart';
import 'package:neorevv/src/core/config/constants.dart';
import 'package:neorevv/src/core/config/responsive.dart';
import 'package:neorevv/src/core/enum/user_role.dart';
import 'package:neorevv/src/core/theme/app_fonts.dart';
import 'package:neorevv/src/core/theme/app_theme.dart';
import 'package:neorevv/src/domain/models/user.dart';
import 'package:neorevv/src/presentation/shared/components/app_textfield.dart';
import 'package:neorevv/src/presentation/shared/components/elevated_button.dart';

class ProfileData extends StatelessWidget {
  final User user;
  final TextEditingController usernameController;
  final TextEditingController currentPasswordController;
  final TextEditingController newPasswordController;
  final TextEditingController confirmPasswordController;

  const ProfileData({
    super.key,
    required this.user,
    required this.usernameController,
    required this.currentPasswordController,
    required this.newPasswordController,
    required this.confirmPasswordController,
  });

  @override
  Widget build(BuildContext context) {
    final isDesktop = Responsive.isDesktop(context);
    final isTablet = Responsive.isTablet(context);
    final isMobile = Responsive.isMobile(context);

    return isMobile
        ? _buildMobileLayout(context, user)
        : _buildDesktopLayout(context, user, isDesktop);
  }

  Widget _buildMobileLayout(BuildContext context, User user) {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildProfileCard(context, user, true),
          const SizedBox(height: defaultPadding),
          _buildOverviewSection(context, true),
          const SizedBox(height: defaultPadding),
          _buildDocumentsSection(context, true),
          const SizedBox(height: defaultPadding),
          _buildAccountDetailsSection(context, true),
        ],
      ),
    );
  }

  // Three-column layout to match the image
  Widget _buildDesktopLayout(BuildContext context, User user, bool isDesktop) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left Column - Profile Card and Change Password
        SizedBox(
          child: Column(
            children: [
              _buildProfileCard(context, user, false),
              const SizedBox(height: defaultPadding),
              _buildChangePasswordCard(context),
            ],
          ),
        ),
        const SizedBox(width: defaultPadding),

        // Middle Column - Overview and Recent Activity
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildOverviewSection(context, false),
              const SizedBox(height: defaultPadding),
              _buildRecentActivitySection(context),
            ],
          ),
        ),
        const SizedBox(width: defaultPadding),

        // Right Column - Documents and Account Details
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDocumentsSection(context, false),
              const SizedBox(height: defaultPadding),
              _buildAccountDetailsSection(context, false),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProfileCard(BuildContext context, User user, bool isMobile) {
    return Container(
      width: isMobile
          ? double.infinity
          : ResponsiveSizes.profileCardWidth(context),
      padding: const EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: AppTheme.primaryBlueColor,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Align(
            alignment: Alignment.topRight,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: defaultPadding * .8,
                vertical: defaultPadding * 0.2,
              ),
              decoration: BoxDecoration(
                color: AppTheme.profileEditBg,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.asset(
                    '$iconAssetpath/edit.png',
                    height: 12,
                    width: 12,
                    color: AppTheme.primaryBlueColor,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'Edit',
                    style: AppFonts.mediumTextStyle(
                      11,
                      color: AppTheme.primaryBlueColor,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Top Row: Profile + Info + Edit
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Profile Image
              CircleAvatar(
                radius: 50,
                child: Image.asset('$iconAssetpath/agent_round.png'),
              ),
              const SizedBox(width: 16),

              // Name, Role, Created
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: AppFonts.semiBoldTextStyle(
                        20,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Image.asset(
                          '$iconAssetpath/fi-rr-portrait.png',
                          height: 14,
                          width: 14,
                          color: AppTheme.profileCardIcon,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Role: ',
                          style: AppFonts.normalTextStyle(
                            12,
                            color: AppTheme.white,
                          ),
                        ),
                        Text(
                          userRoleToString(user.role),
                          style: AppFonts.regularTextStyle(
                            12,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Image.asset(
                          '$iconAssetpath/fi-rs-calendar.png',
                          height: 14,
                          width: 14,
                          color: AppTheme.profileCardIcon,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Created: ',
                          style: AppFonts.normalTextStyle(
                            12,
                            color: AppTheme.white,
                          ),
                        ),
                        Text(
                          '02/25/2023',
                          style: AppFonts.regularTextStyle(
                            12,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Contact Card
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.profileContactBg,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                _buildContactInfo(
                  '$iconAssetpath/fi-rr-phone-call.png',
                  user.phone,
                  isMobile,
                ),
                const SizedBox(height: 8),
                _buildContactInfo(
                  '$iconAssetpath/profile_mail.png',
                  user.email,
                  isMobile,
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Grid Info Section
          Padding(
            padding: const EdgeInsets.only(
              left: defaultPadding,
              right: defaultPadding,
              bottom: defaultPadding,
            ),
            child: Wrap(
              spacing: 16,
              runSpacing: 12,
              // alignment: WrapAlignment.center,
              children: [
                _buildInfoItem(
                  '$iconAssetpath/fi-rs-calendar.png',
                  'DOB:',
                  '06/28/1999',
                ),
                _buildInfoItem(
                  '$iconAssetpath/fi-rr-user.png',
                  'Gender:',
                  'Male',
                ),
                _buildInfoItem(
                  '$iconAssetpath/fi-rr-map-marker.png',
                  'State:',
                  'California',
                ),
                _buildInfoItem(
                  '$iconAssetpath/fi-rr-map-marker-home.png',
                  'City:',
                  'Berkeley',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Small reusable item
  Widget _buildInfoItem(String iconPath, String label, String value) {
    return SizedBox(
      width: 160,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            iconPath,
            height: 14,
            width: 14,
            color: AppTheme.profileCardIcon,
          ),
          const SizedBox(width: 6),
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '$label ',
                  style: AppFonts.normalTextStyle(12, color: Colors.white),
                ),
                TextSpan(
                  text: value,
                  style: AppFonts.regularTextStyle(12, color: Colors.white),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactInfo(String iconPath, String text, bool isMobile) {
    return Row(
      children: [
        Image.asset(
          iconPath,
          height: 14,
          width: 14,
          color: AppTheme.profileCardIcon,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: AppFonts.regularTextStyle(
              isMobile ? 12 : 13,
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildChangePasswordCard(BuildContext context) {
    return SizedBox(
      width: ResponsiveSizes.changePasswordWidth(context),
      child: Column(
        children: [
          Container(
            width: ResponsiveSizes.changePasswordWidth(context),
            height: 80,//ResponsiveSizes.changePasswordHeaderHeight(context),
            // padding: const EdgeInsets.all(primaryLayoutPadding),
            decoration: BoxDecoration(
              color: AppTheme.primaryBlueColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(15),
                topRight: Radius.circular(15),
              ),
            ),
            child: Center(
              child: Text(
                'Change Password',
                style: AppFonts.mediumTextStyle(18, color: Colors.white),
              ),
            ),
          ),
          // Content Section
          Container(
            width: ResponsiveSizes.changePasswordWidth(context),
            height: ResponsiveSizes.changePasswordContentHeight(context),
             margin: const EdgeInsets.only(top: -20),
            padding: const EdgeInsets.all(primaryLayoutPadding),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(10),
                topRight: Radius.circular(10),
                bottomLeft: Radius.circular(25),
                bottomRight: Radius.circular(25),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Username field
                Text(
                  'Username',
                  style: AppFonts.mediumTextStyle(
                    14,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  decoration: BoxDecoration(
                    color: AppTheme.greyRoundBg,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: AppTextField(
                    controller: usernameController,
                    hintText: 'Enter username',
                    isMobile: false,
                  ),
                ),
                const SizedBox(height: defaultPadding),

                // Current Password field
                Text(
                  'Current Password',
                  style: AppFonts.mediumTextStyle(
                    14,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 8),
                AppTextField(
                  controller: currentPasswordController,
                  hintText: 'Enter current password',
                  isObscure: true,
                  showToggle: true,
                  isMobile: false,
                ),
                const SizedBox(height: defaultPadding),

                // New Password field
                Text(
                  'New Password',
                  style: AppFonts.mediumTextStyle(
                    14,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 8),
                AppTextField(
                  controller: newPasswordController,
                  hintText: 'Enter new password',
                  isObscure: true,
                  showToggle: true,
                  isMobile: false,
                ),
                const SizedBox(height: defaultPadding),

                // Confirm New Password field
                Text(
                  'Enter New Password',
                  style: AppFonts.mediumTextStyle(
                    14,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 8),
                AppTextField(
                  controller: confirmPasswordController,
                  hintText: 'Enter new password again',
                  isObscure: true,
                  showToggle: true,
                  isMobile: false,
                ),
                const SizedBox(height: defaultPadding),

                // Buttons
                Row(
                  children: [
                    Expanded(
                      child: AppButton(
                        label: 'Clear',
                        onPressed: () {
                          currentPasswordController.clear();
                          newPasswordController.clear();
                          confirmPasswordController.clear();
                        },
                        backgroundColor: AppTheme.cancelBgColor,
                        foregroundColor: AppTheme.primaryTextColor,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: AppButton(
                        label: 'Submit',
                        onPressed: () {
                          // TODO: Implement password change logic
                        },
                        backgroundColor: AppTheme.primaryBlueColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Replace the _buildOverviewSection method:
  Widget _buildOverviewSection(BuildContext context, bool isMobile) {
    return Column(
      children: [
        // Header Section with blue background
        Container(
          height: ResponsiveSizes.overviewHeaderHeight(context),
          padding: const EdgeInsets.all(primaryLayoutPadding),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [AppTheme.primaryColor, AppTheme.primaryBlueColor],
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(15),
              topRight: Radius.circular(15),
            ),
          ),
          child: Center(
            child: Text(
              'Overview',
              style: AppFonts.boldTextStyle(18, color: Colors.white),
            ),
          ),
        ),
        // Content Section
        Container(
          height: ResponsiveSizes.overviewContentHeight(context),
          padding: const EdgeInsets.all(primaryLayoutPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(15),
              bottomRight: Radius.circular(15),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: isMobile ? 2 : 4,
            crossAxisSpacing: defaultPadding,
            mainAxisSpacing: defaultPadding,
            childAspectRatio: isMobile ? 1.5 : 2.0,
            children: [
              _buildStatCard('Total Brokerages', '168', Icons.business),
              _buildStatCard('Total Agents', '2850', Icons.people),
              _buildStatCard('Total Sales', '20K', Icons.trending_up),
              _buildStatCard('Total Revenue', '\$250K', Icons.attach_money),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: AppTheme.scaffoldBgColor,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 24, color: AppTheme.primaryBlueColor),
          const SizedBox(height: 8),
          Text(
            title,
            style: AppFonts.regularTextStyle(
              12,
              color: AppTheme.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppFonts.boldTextStyle(16, color: AppTheme.primaryTextColor),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Replace the _buildRecentActivitySection method:
  Widget _buildRecentActivitySection(BuildContext context) {
    final activities = [
      {
        'title': 'Agent Mark Lee created by Broker Linda K',
        'date': '07/22/2025',
        'icon': Icons.person_add,
      },
      {
        'title': 'Commission rules updated by Admin John D',
        'date': '07/21/2025',
        'icon': Icons.rule,
      },
      {
        'title':
            'Document uploaded: Brokerage_A_License.pdf by Agent David Thompson',
        'date': '07/21/2025',
        'icon': Icons.upload_file,
      },
      {
        'title': 'Bank details verified by Broker Marvin McKinney',
        'date': '07/20/2025',
        'icon': Icons.verified,
      },
      {
        'title': 'Bank Detail Update by Admin John D',
        'date': '07/18/2025',
        'icon': Icons.account_balance,
      },
      {
        'title': 'Added agent Mark Lee by Broker Lisa',
        'date': '07/17/2025',
        'icon': Icons.person_add,
      },
    ];

    return Column(
      children: [
        // Header Section with blue background
        Container(
          height: ResponsiveSizes.recentActivityHeaderHeight(context),
          padding: const EdgeInsets.all(primaryLayoutPadding),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [AppTheme.primaryColor, AppTheme.primaryBlueColor],
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(15),
              topRight: Radius.circular(15),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Activity',
                style: AppFonts.boldTextStyle(18, color: Colors.white),
              ),
              TextButton(
                onPressed: () {},
                child: Text(
                  'View All',
                  style: AppFonts.mediumTextStyle(14, color: Colors.white),
                ),
              ),
            ],
          ),
        ),
        // Content Section
        Container(
          height: ResponsiveSizes.recentActivityContentHeight(context),
          padding: const EdgeInsets.all(primaryLayoutPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(15),
              bottomRight: Radius.circular(15),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: SingleChildScrollView(
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: activities.length,
              separatorBuilder: (context, index) => const SizedBox(height: 12),
              itemBuilder: (context, index) {
                final activity = activities[index];
                return _buildActivityItem(
                  activity['title'] as String,
                  activity['date'] as String,
                  activity['icon'] as IconData,
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActivityItem(String title, String date, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppTheme.scaffoldBgColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, size: 16, color: AppTheme.primaryBlueColor),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppFonts.regularTextStyle(
                  13,
                  color: AppTheme.primaryTextColor,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              Text(
                date,
                style: AppFonts.regularTextStyle(
                  11,
                  color: AppTheme.secondaryTextColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Replace the _buildDocumentsSection method:
  Widget _buildDocumentsSection(BuildContext context, bool isMobile) {
    final documents = [
      {'name': 'Brokerage Sales Volume Report.pdf', 'color': Colors.red},
      {'name': 'Commission Distribution Report.pdf', 'color': Colors.red},
      {'name': 'Transaction Status Report.pdf', 'color': Colors.red},
      {'name': 'Listing Activity Report.pdf', 'color': Colors.red},
      {'name': 'Escrow and Deposit Report.pdf', 'color': Colors.red},
    ];

    return Column(
      children: [
        // Header Section with blue background
        Container(
          height: ResponsiveSizes.documentsHeaderHeight(context),
          padding: const EdgeInsets.all(primaryLayoutPadding),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [AppTheme.primaryColor, AppTheme.primaryBlueColor],
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(15),
              topRight: Radius.circular(15),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Documents',
                style: AppFonts.boldTextStyle(18, color: Colors.white),
              ),
              if (!isMobile)
                TextButton(
                  onPressed: () {},
                  child: Text(
                    'Edit',
                    style: AppFonts.mediumTextStyle(14, color: Colors.white),
                  ),
                ),
            ],
          ),
        ),
        // Content Section
        Container(
          height: ResponsiveSizes.documentsContentHeight(context),
          padding: const EdgeInsets.all(primaryLayoutPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(15),
              bottomRight: Radius.circular(15),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              // Documents List
              Expanded(
                child: ListView.separated(
                  shrinkWrap: true,
                  itemCount: documents.length,
                  separatorBuilder: (context, index) =>
                      const SizedBox(height: 8),
                  itemBuilder: (context, index) {
                    final doc = documents[index];
                    return _buildDocumentItem(
                      doc['name'] as String,
                      doc['color'] as Color,
                    );
                  },
                ),
              ),
              if (!isMobile) ...[
                const SizedBox(height: defaultPadding),
                TextButton(
                  onPressed: () {},
                  child: Text(
                    'View All',
                    style: AppFonts.mediumTextStyle(
                      14,
                      color: AppTheme.primaryBlueColor,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDocumentItem(String name, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.scaffoldBgColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(Icons.picture_as_pdf, color: color, size: 16),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              name,
              style: AppFonts.regularTextStyle(
                13,
                color: AppTheme.primaryTextColor,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.visibility_outlined, size: 18),
            color: AppTheme.primaryBlueColor,
            constraints: const BoxConstraints(),
            padding: EdgeInsets.zero,
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.download_outlined, size: 18),
            color: AppTheme.primaryBlueColor,
            constraints: const BoxConstraints(),
            padding: EdgeInsets.zero,
          ),
        ],
      ),
    );
  }

  // Replace the _buildAccountDetailsSection method:
  Widget _buildAccountDetailsSection(BuildContext context, bool isMobile) {
    final accountDetails = [
      {'label': 'Bank Name', 'value': 'Chase Bank'},
      {'label': 'Account Holder Name', 'value': 'Nabil Sayed'},
      {'label': 'Account Type', 'value': 'Savings'},
      {'label': 'Account Number', 'value': '•••• •••• 1234'},
      {'label': 'Bank Country', 'value': 'United States'},
      {'label': 'Currency', 'value': 'USD'},
    ];

    return Column(
      children: [
        // Header Section with blue background
        Container(
          height: ResponsiveSizes.accountDetailsHeaderHeight(context),
          padding: const EdgeInsets.all(primaryLayoutPadding),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [AppTheme.primaryColor, AppTheme.primaryBlueColor],
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(15),
              topRight: Radius.circular(15),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Account Details',
                style: AppFonts.boldTextStyle(18, color: Colors.white),
              ),
              if (!isMobile)
                TextButton(
                  onPressed: () {},
                  child: Text(
                    'Edit',
                    style: AppFonts.mediumTextStyle(14, color: Colors.white),
                  ),
                ),
            ],
          ),
        ),
        // Content Section
        Container(
          height: ResponsiveSizes.accountDetailsContentHeight(context),
          padding: const EdgeInsets.all(primaryLayoutPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(15),
              bottomRight: Radius.circular(15),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: accountDetails.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final detail = accountDetails[index];
              return _buildAccountDetailItem(
                detail['label'] as String,
                detail['value'] as String,
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAccountDetailItem(String label, String value) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppTheme.scaffoldBgColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getIconForLabel(label),
            size: 16,
            color: AppTheme.primaryBlueColor,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppFonts.regularTextStyle(
                  12,
                  color: AppTheme.secondaryTextColor,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: AppFonts.mediumTextStyle(
                  14,
                  color: AppTheme.primaryTextColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  IconData _getIconForLabel(String label) {
    switch (label.toLowerCase()) {
      case 'bank name':
        return Icons.account_balance;
      case 'account holder name':
        return Icons.person;
      case 'account type':
        return Icons.account_box;
      case 'account number':
        return Icons.numbers;
      case 'bank country':
        return Icons.flag;
      case 'currency':
        return Icons.attach_money;
      default:
        return Icons.info;
    }
  }
}

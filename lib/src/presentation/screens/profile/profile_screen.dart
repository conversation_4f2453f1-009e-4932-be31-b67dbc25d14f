import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/presentation/screens/profile/components/profile.dart';

import '../dashboard/components/dashboard_content.dart'; // For Footer
import '../../../core/config/constants.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/enum/user_role.dart';
import '../../../domain/models/user.dart';

class ProfileScreen extends HookWidget {
  final bool includeScaffold;

  const ProfileScreen({super.key, this.includeScaffold = true});

  @override
  Widget build(BuildContext context) {
    // TODO: Replace with actual user data from state management
    final User user = User(
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      phone: "(*************",
      image: "$iconAssetpath/agent_round.png",
      role: UserRole.platformOwner,
    );

    // Define hooks for password change form
    final usernameController = useTextEditingController(text: user.email);
    final currentPasswordController = useTextEditingController();
    final newPasswordController = useTextEditingController();
    final confirmPasswordController = useTextEditingController();

    final profileContent = Column(
      children: [
        const SizedBox(height: defaultPadding),
        // Profile Content
        ProfileData(
          user: user,
          usernameController: usernameController,
          currentPasswordController: currentPasswordController,
          newPasswordController: newPasswordController,
          confirmPasswordController: confirmPasswordController,
        ),
        // Footer (only when used as standalone screen)
        if (includeScaffold) const Footer(),
      ],
    );

    if (includeScaffold) {
      return Scaffold(
        backgroundColor: AppTheme.scaffoldBgColor,
        body: SafeArea(
          child: SingleChildScrollView(
            child: profileContent,
          ),
        ),
      );
    } else {
      // Return just the content when used within main layout
      return profileContent;
    }
  }




}

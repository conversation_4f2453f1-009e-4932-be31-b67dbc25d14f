import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../core/config/constants.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';

class AgentProfileScreen extends HookWidget {
  const AgentProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Left Profile Card
            Container(
              width: 280,
              child: _buildProfileCard(),
            ),
            const SizedBox(width: 24),
            // Right Content Area
            Expanded(
              child: _buildContentArea(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          // Profile Image and Name
          CircleAvatar(
            radius: 40,
            backgroundImage: AssetImage('$iconAssetpath/agent_round.png'),
          ),
          const SizedBox(height: 16),
          Text(
            'Jessica Miller',
            style: AppFonts.boldTextStyle(20, color: Colors.white),
          ),
          const SizedBox(height: 8),
          
          // Role Badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.person, size: 14, color: Colors.white),
                const SizedBox(width: 4),
                Text('Role: Agent', style: AppFonts.regularTextStyle(12, color: Colors.white)),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text('Level 1', style: AppFonts.regularTextStyle(10, color: Colors.white)),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          
          // Join Date
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.calendar_today, size: 14, color: Colors.white70),
              const SizedBox(width: 4),
              Text('Join Date: 07/14/2024', style: AppFonts.regularTextStyle(12, color: Colors.white70)),
            ],
          ),
          const SizedBox(height: 24),
          
          // Contact Info
          _buildContactItem(Icons.phone, '(226) beg-qua'),
          const SizedBox(height: 12),
          _buildContactItem(Icons.email, '<EMAIL>'),
          const SizedBox(height: 24),
          
          // Stats
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.location_on, size: 14, color: Colors.white70),
                        const SizedBox(width: 4),
                        Text('State:', style: AppFonts.regularTextStyle(12, color: Colors.white70)),
                      ],
                    ),
                    Text('California', style: AppFonts.mediumTextStyle(14, color: Colors.white)),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.location_city, size: 14, color: Colors.white70),
                        const SizedBox(width: 4),
                        Text('City:', style: AppFonts.regularTextStyle(12, color: Colors.white70)),
                      ],
                    ),
                    Text('Berkeley', style: AppFonts.mediumTextStyle(14, color: Colors.white)),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.handshake, size: 14, color: Colors.white70),
                        const SizedBox(width: 4),
                        Text('Total Deals:', style: AppFonts.regularTextStyle(12, color: Colors.white70)),
                      ],
                    ),
                    Text('15', style: AppFonts.boldTextStyle(16, color: Colors.white)),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.attach_money, size: 14, color: Colors.white70),
                        const SizedBox(width: 4),
                        Text('Earning:', style: AppFonts.regularTextStyle(12, color: Colors.white70)),
                      ],
                    ),
                    Text('\$5547.00', style: AppFonts.boldTextStyle(16, color: Colors.white)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContactItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.white70),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: AppFonts.regularTextStyle(12, color: Colors.white),
          ),
        ),
      ],
    );
  }

  Widget _buildContentArea() {
    return Column(
      children: [
        // Header
        Row(
          children: [
            Text(
              'Agent Profile',
              style: AppFonts.boldTextStyle(24, color: AppTheme.primaryTextColor),
            ),
            const Spacer(),
            Text(
              'Documents',
              style: AppFonts.boldTextStyle(18, color: AppTheme.primaryTextColor),
            ),
          ],
        ),
        const SizedBox(height: 24),
        
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Left Content
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildAgentBio(),
                  const SizedBox(height: 24),
                  _buildExperienceHighlights(),
                  const SizedBox(height: 24),
                  _buildCertifications(),
                ],
              ),
            ),
            const SizedBox(width: 24),
            // Right Documents
            Expanded(
              flex: 1,
              child: _buildDocuments(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAgentBio() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.person_outline, size: 20, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              Text(
                'Agent Bio',
                style: AppFonts.boldTextStyle(16, color: AppTheme.primaryTextColor),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Jessica Miller is a seasoned real estate professional with over 12 years of experience helping buyers, sellers, and investors in the Greater Los Angeles area. Known for her personalized approach, strong negotiation skills, and in-depth market knowledge, Jessica has successfully closed over 350 transactions, including luxury estates, multi-family investments, and first-time homebuyer deals.',
            style: AppFonts.regularTextStyle(14, color: AppTheme.secondaryTextColor).copyWith(height: 1.5),
          ),
        ],
      ),
    );
  }

  Widget _buildExperienceHighlights() {
    final highlights = [
      'Residential Sales',
      'Luxury Properties',
      'First-Time Home Buyers',
      'Relocation Services',
      'Investment Properties',
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.star_outline, size: 20, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              Text(
                'Experience Highlights',
                style: AppFonts.boldTextStyle(16, color: AppTheme.primaryTextColor),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: highlights.map((highlight) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFFF0F4FF),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: AppTheme.primaryColor.withOpacity(0.2)),
              ),
              child: Text(
                highlight,
                style: AppFonts.regularTextStyle(12, color: AppTheme.primaryColor),
              ),
            )).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildCertifications() {
    final certifications = [
      'E&O Insurance Certificate',
      'Brokerage License',
      'Principal Broker ID',
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.verified_outlined, size: 20, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              Text(
                'Certifications',
                style: AppFonts.boldTextStyle(16, color: AppTheme.primaryTextColor),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: certifications.map((cert) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFFF8F9FA),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.grey.withOpacity(0.3)),
              ),
              child: Text(
                cert,
                style: AppFonts.regularTextStyle(12, color: AppTheme.secondaryTextColor),
              ),
            )).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildDocuments() {
    final documents = [
      {'name': 'E&O Insurance Certificate', 'color': Colors.red},
      {'name': 'Brokerage License', 'color': Colors.red},
      {'name': 'Principal Broker ID', 'color': Colors.red},
    ];

    return Column(
      children: documents.map((doc) => Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: (doc['color'] as Color).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.description,
                color: doc['color'] as Color,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                doc['name'] as String,
                style: AppFonts.mediumTextStyle(14, color: AppTheme.primaryTextColor),
              ),
            ),
            IconButton(
              onPressed: () {},
              icon: const Icon(Icons.visibility_outlined, size: 20),
              color: AppTheme.primaryColor,
            ),
            IconButton(
              onPressed: () {},
              icon: const Icon(Icons.download_outlined, size: 20),
              color: AppTheme.primaryColor,
            ),
          ],
        ),
      )).toList(),
    );
  }
}
